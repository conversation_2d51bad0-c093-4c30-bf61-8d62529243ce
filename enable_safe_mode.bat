@echo off
echo ========================================
echo HVNC Safe Mode Configuration
echo ========================================
echo.
echo This will switch to safe mode configuration
echo which disables advanced optimizations that
echo might cause crashes on some systems.
echo.
echo Safe mode features:
echo - Basic JPEG quality (50%%)
echo - Conservative frame rate (30 FPS)
echo - Hardware acceleration disabled
echo - Windows 11 optimizations disabled
echo - Adaptive quality disabled
echo.
set /p confirm="Enable safe mode? (y/n): "

if /i "%confirm%"=="y" (
    copy /y hvnc_performance_safe.ini hvnc_performance.ini
    echo.
    echo Safe mode enabled successfully!
    echo The HVNC applications will now use conservative settings.
    echo.
    echo To restore optimized settings, run configure_performance.bat
    echo.
) else (
    echo Safe mode not enabled.
)

pause
