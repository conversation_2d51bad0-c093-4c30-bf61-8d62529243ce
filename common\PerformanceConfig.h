#pragma once
#include <Windows.h>

// Performance configuration constants
namespace PerformanceConfig {
    // Image quality presets
    enum QualityPreset {
        PRESET_ULTRA_FAST = 0,  // Lowest quality, highest speed
        PRESET_FAST = 1,        // Low quality, high speed
        PRESET_BALANCED = 2,    // Medium quality, good speed
        PRESET_QUALITY = 3,     // High quality, lower speed
        PRESET_ULTRA_QUALITY = 4 // Highest quality, lowest speed
    };

    // Quality preset configurations
    struct QualityConfig {
        ULONG jpegQuality;      // 1-100
        DWORD frameRateLimit;   // FPS limit
        DWORD compressionLevel; // 1-9
        BOOL useHardwareAccel;  // Hardware acceleration
        BOOL adaptiveQuality;   // Adaptive quality adjustment
    };

    // Predefined quality configurations
    static const QualityConfig QUALITY_PRESETS[] = {
        // ULTRA_FAST
        { 25, 30, 3, TRUE, TRUE },
        // FAST  
        { 40, 45, 4, TRUE, TRUE },
        // BALANCED
        { 60, 60, 6, TRUE, TRUE },
        // QUALITY
        { 80, 60, 8, TRUE, FALSE },
        // ULTRA_QUALITY
        { 95, 30, 9, FALSE, <PERSON>LS<PERSON> }
    };

    // Windows version specific optimizations
    struct WindowsOptimizations {
        BOOL disableAero;           // Disable Aero effects
        BOOL disableAnimations;     // Disable window animations
        BOOL useDirectCapture;      // Use direct screen capture
        BOOL optimizeForWindows11;  // Windows 11 specific optimizations
    };

    // Network optimization settings
    struct NetworkConfig {
        DWORD sendBufferSize;       // Socket send buffer size
        DWORD receiveBufferSize;    // Socket receive buffer size
        BOOL useCompression;        // Enable data compression
        DWORD compressionThreshold; // Minimum size for compression
    };

    // Complete performance configuration
    struct PerformanceProfile {
        QualityConfig quality;
        WindowsOptimizations windows;
        NetworkConfig network;
        char profileName[64];
    };

    // Predefined performance profiles
    static const PerformanceProfile PERFORMANCE_PROFILES[] = {
        // Gaming profile - prioritize speed
        {
            QUALITY_PRESETS[PRESET_FAST],
            { TRUE, TRUE, TRUE, TRUE },
            { 65536, 65536, TRUE, 1024 },
            "Gaming"
        },
        // Office profile - balanced
        {
            QUALITY_PRESETS[PRESET_BALANCED],
            { FALSE, FALSE, TRUE, TRUE },
            { 32768, 32768, TRUE, 2048 },
            "Office"
        },
        // Design profile - prioritize quality
        {
            QUALITY_PRESETS[PRESET_QUALITY],
            { FALSE, FALSE, FALSE, TRUE },
            { 16384, 16384, FALSE, 0 },
            "Design"
        }
    };

    // Configuration file functions
    BOOL LoadConfigFromFile(const char* filename, PerformanceProfile* profile);
    BOOL SaveConfigToFile(const char* filename, const PerformanceProfile* profile);
    void ApplyPerformanceProfile(const PerformanceProfile* profile);
    void GetCurrentProfile(PerformanceProfile* profile);
    
    // Utility functions
    const char* GetPresetName(QualityPreset preset);
    QualityPreset GetPresetFromName(const char* name);
    void OptimizeForCurrentSystem(PerformanceProfile* profile);
}
