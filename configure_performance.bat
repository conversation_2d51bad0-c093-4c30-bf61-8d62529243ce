@echo off
echo ========================================
echo HVNC Performance Configuration Tool
echo ========================================
echo.

REM Check if config file exists
if exist "hvnc_performance.ini" (
    echo Current configuration found.
    echo.
) else (
    echo No configuration found. Creating default configuration...
    echo.
)

echo Select a performance preset:
echo.
echo 1. Gaming - Maximum speed (JPEG Quality: 40, FPS: 45)
echo 2. Office - Balanced performance (JPEG Quality: 60, FPS: 60) 
echo 3. Design - Best quality (JPEG Quality: 85, FPS: 30)
echo 4. Custom - Manual configuration
echo 5. Exit without changes
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto gaming
if "%choice%"=="2" goto office  
if "%choice%"=="3" goto design
if "%choice%"=="4" goto custom
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
pause
goto :eof

:gaming
echo Creating Gaming preset configuration...
(
echo # HVNC Performance Configuration - Gaming Preset
echo # Optimized for maximum speed and responsiveness
echo.
echo # Image Quality Settings
echo jpeg_quality=40
echo frame_rate=45
echo compression_level=4
echo hardware_accel=true
echo adaptive_quality=true
echo.
echo # Windows Optimizations
echo disable_aero=false
echo disable_animations=false
echo direct_capture=true
echo windows11_optimizations=true
echo.
echo # Network Settings
echo send_buffer_size=65536
echo receive_buffer_size=65536
echo use_compression=true
echo compression_threshold=1024
echo.
echo profile_name=Gaming
) > hvnc_performance.ini
echo Gaming configuration created successfully!
goto success

:office
echo Creating Office preset configuration...
(
echo # HVNC Performance Configuration - Office Preset
echo # Balanced performance for office work
echo.
echo # Image Quality Settings
echo jpeg_quality=60
echo frame_rate=60
echo compression_level=6
echo hardware_accel=true
echo adaptive_quality=true
echo.
echo # Windows Optimizations
echo disable_aero=false
echo disable_animations=false
echo direct_capture=true
echo windows11_optimizations=true
echo.
echo # Network Settings
echo send_buffer_size=32768
echo receive_buffer_size=32768
echo use_compression=true
echo compression_threshold=2048
echo.
echo profile_name=Office
) > hvnc_performance.ini
echo Office configuration created successfully!
goto success

:design
echo Creating Design preset configuration...
(
echo # HVNC Performance Configuration - Design Preset
echo # Optimized for best image quality
echo.
echo # Image Quality Settings
echo jpeg_quality=85
echo frame_rate=30
echo compression_level=8
echo hardware_accel=false
echo adaptive_quality=false
echo.
echo # Windows Optimizations
echo disable_aero=false
echo disable_animations=false
echo direct_capture=false
echo windows11_optimizations=true
echo.
echo # Network Settings
echo send_buffer_size=16384
echo receive_buffer_size=16384
echo use_compression=false
echo compression_threshold=0
echo.
echo profile_name=Design
) > hvnc_performance.ini
echo Design configuration created successfully!
goto success

:custom
echo.
echo Custom Configuration
echo ====================
echo.
set /p jpeg_quality="JPEG Quality (1-100, recommended 30-85): "
set /p frame_rate="Frame Rate (15-120, recommended 30-60): "
set /p compression="Compression Level (1-9, recommended 3-8): "
echo.
echo Enable Hardware Acceleration?
set /p hw_accel="(y/n, recommended y): "
if /i "%hw_accel%"=="y" (set hw_accel=true) else (set hw_accel=false)
echo.
echo Enable Windows 11 Optimizations?
set /p win11_opt="(y/n, recommended y for Windows 11): "
if /i "%win11_opt%"=="y" (set win11_opt=true) else (set win11_opt=false)
echo.
echo Enable Network Compression?
set /p net_comp="(y/n, recommended y for slow connections): "
if /i "%net_comp%"=="y" (set net_comp=true) else (set net_comp=false)

echo Creating custom configuration...
(
echo # HVNC Performance Configuration - Custom
echo # User-defined settings
echo.
echo # Image Quality Settings
echo jpeg_quality=%jpeg_quality%
echo frame_rate=%frame_rate%
echo compression_level=%compression%
echo hardware_accel=%hw_accel%
echo adaptive_quality=true
echo.
echo # Windows Optimizations
echo disable_aero=false
echo disable_animations=false
echo direct_capture=true
echo windows11_optimizations=%win11_opt%
echo.
echo # Network Settings
echo send_buffer_size=32768
echo receive_buffer_size=32768
echo use_compression=%net_comp%
echo compression_threshold=1024
echo.
echo profile_name=Custom
) > hvnc_performance.ini
echo Custom configuration created successfully!
goto success

:success
echo.
echo ========================================
echo Configuration Complete!
echo ========================================
echo.
echo Configuration saved to: hvnc_performance.ini
echo.
echo The HVNC applications will automatically use these settings.
echo You can edit hvnc_performance.ini manually for fine-tuning.
echo.
echo For more information, see PERFORMANCE_GUIDE.md
echo.
pause
goto end

:exit
echo No changes made.
pause
goto end

:end
