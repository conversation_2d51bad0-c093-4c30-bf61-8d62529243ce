@echo off
echo Building Performance Optimizer...

REM Find MSBuild
set "MSBUILD_PATH="
for /f "usebackq tokens=*" %%i in (`"%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe" -latest -products * -requires Microsoft.Component.MSBuild -property installationPath`) do (
    set "MSBUILD_PATH=%%i\MSBuild\Current\Bin\MSBuild.exe"
)

if not exist "%MSBUILD_PATH%" (
    echo MSBuild not found. Please install Visual Studio 2019 or later.
    exit /b 1
)

REM Create tools directory if it doesn't exist
if not exist "tools\build" mkdir "tools\build"

REM Use MSBuild to compile the optimizer
echo Compiling Performance Optimizer...
"%MSBUILD_PATH%" /p:Configuration=Release /p:Platform=Win32 /p:OutDir=tools\build\ /p:IntDir=tools\temp\ /target:Build << EOF
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <OutDir>tools\build\</OutDir>
    <IntDir>tools\temp\</IntDir>
    <TargetName>PerformanceOptimizer</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="tools\PerformanceOptimizer.cpp" />
    <ClCompile Include="common\PerformanceConfig.cpp" />
  </ItemGroup>
</Project>
EOF

if errorlevel 1 (
    echo Performance Optimizer build failed.
    echo Trying alternative method...
    
    REM Try using the Developer Command Prompt environment
    call "%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" -arch=x86 -host_arch=x64
    
    cl /EHsc /O2 /MT /I. tools\PerformanceOptimizer.cpp common\PerformanceConfig.cpp /Fe:tools\build\PerformanceOptimizer.exe /Fo:tools\temp\
    
    if errorlevel 1 (
        echo Performance Optimizer build failed with both methods.
        echo You can still use the main HVNC applications and manually edit hvnc_performance.ini
        exit /b 1
    )
)

echo Performance Optimizer built successfully!
echo Run tools\build\PerformanceOptimizer.exe to configure performance settings.
