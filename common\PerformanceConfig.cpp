#include "PerformanceConfig.h"
#include "Api.h"
#include <stdio.h>

namespace PerformanceConfig {

    // Current active profile
    static PerformanceProfile g_currentProfile = PERFORMANCE_PROFILES[1]; // Default to Office profile

    BOOL LoadConfigFromFile(const char* filename, PerformanceProfile* profile)
    {
        if (!filename || !profile) return FALSE;

        FILE* file = fopen(filename, "r");
        if (!file) return FALSE;

        char line[256];
        while (fgets(line, sizeof(line), file)) {
            // Skip comments and empty lines
            if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') continue;

            char key[64], value[64];
            if (sscanf(line, "%63[^=]=%63s", key, value) == 2) {
                // Quality settings
                if (strcmp(key, "jpeg_quality") == 0) {
                    profile->quality.jpegQuality = atoi(value);
                } else if (strcmp(key, "frame_rate") == 0) {
                    profile->quality.frameRateLimit = atoi(value);
                } else if (strcmp(key, "compression_level") == 0) {
                    profile->quality.compressionLevel = atoi(value);
                } else if (strcmp(key, "hardware_accel") == 0) {
                    profile->quality.useHardwareAccel = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "adaptive_quality") == 0) {
                    profile->quality.adaptiveQuality = (strcmp(value, "true") == 0);
                }
                // Windows optimizations
                else if (strcmp(key, "disable_aero") == 0) {
                    profile->windows.disableAero = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "disable_animations") == 0) {
                    profile->windows.disableAnimations = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "direct_capture") == 0) {
                    profile->windows.useDirectCapture = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "windows11_optimizations") == 0) {
                    profile->windows.optimizeForWindows11 = (strcmp(value, "true") == 0);
                }
                // Network settings
                else if (strcmp(key, "send_buffer_size") == 0) {
                    profile->network.sendBufferSize = atoi(value);
                } else if (strcmp(key, "receive_buffer_size") == 0) {
                    profile->network.receiveBufferSize = atoi(value);
                } else if (strcmp(key, "use_compression") == 0) {
                    profile->network.useCompression = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "compression_threshold") == 0) {
                    profile->network.compressionThreshold = atoi(value);
                } else if (strcmp(key, "profile_name") == 0) {
                    strncpy(profile->profileName, value, sizeof(profile->profileName) - 1);
                    profile->profileName[sizeof(profile->profileName) - 1] = '\0';
                }
            }
        }

        fclose(file);
        return TRUE;
    }

    BOOL SaveConfigToFile(const char* filename, const PerformanceProfile* profile)
    {
        if (!filename || !profile) return FALSE;

        FILE* file = fopen(filename, "w");
        if (!file) return FALSE;

        fprintf(file, "# HVNC Performance Configuration\n");
        fprintf(file, "# Profile: %s\n\n", profile->profileName);

        fprintf(file, "# Image Quality Settings\n");
        fprintf(file, "jpeg_quality=%lu\n", profile->quality.jpegQuality);
        fprintf(file, "frame_rate=%lu\n", profile->quality.frameRateLimit);
        fprintf(file, "compression_level=%lu\n", profile->quality.compressionLevel);
        fprintf(file, "hardware_accel=%s\n", profile->quality.useHardwareAccel ? "true" : "false");
        fprintf(file, "adaptive_quality=%s\n\n", profile->quality.adaptiveQuality ? "true" : "false");

        fprintf(file, "# Windows Optimizations\n");
        fprintf(file, "disable_aero=%s\n", profile->windows.disableAero ? "true" : "false");
        fprintf(file, "disable_animations=%s\n", profile->windows.disableAnimations ? "true" : "false");
        fprintf(file, "direct_capture=%s\n", profile->windows.useDirectCapture ? "true" : "false");
        fprintf(file, "windows11_optimizations=%s\n\n", profile->windows.optimizeForWindows11 ? "true" : "false");

        fprintf(file, "# Network Settings\n");
        fprintf(file, "send_buffer_size=%lu\n", profile->network.sendBufferSize);
        fprintf(file, "receive_buffer_size=%lu\n", profile->network.receiveBufferSize);
        fprintf(file, "use_compression=%s\n", profile->network.useCompression ? "true" : "false");
        fprintf(file, "compression_threshold=%lu\n\n", profile->network.compressionThreshold);

        fprintf(file, "profile_name=%s\n", profile->profileName);

        fclose(file);
        return TRUE;
    }

    void ApplyPerformanceProfile(const PerformanceProfile* profile)
    {
        if (!profile) return;

        g_currentProfile = *profile;
        
        // Apply quality settings (these would be used by the capture system)
        // Implementation would depend on how the capture system is structured
        
        // Apply Windows optimizations
        if (profile->windows.disableAnimations) {
            // Disable window animations for better performance
            ANIMATIONINFO ai = { sizeof(ANIMATIONINFO), FALSE };
            SystemParametersInfo(SPI_SETANIMATION, sizeof(ai), &ai, SPIF_SENDCHANGE);
        }
    }

    void GetCurrentProfile(PerformanceProfile* profile)
    {
        if (profile) {
            *profile = g_currentProfile;
        }
    }

    const char* GetPresetName(QualityPreset preset)
    {
        switch (preset) {
            case PRESET_ULTRA_FAST: return "Ultra Fast";
            case PRESET_FAST: return "Fast";
            case PRESET_BALANCED: return "Balanced";
            case PRESET_QUALITY: return "Quality";
            case PRESET_ULTRA_QUALITY: return "Ultra Quality";
            default: return "Unknown";
        }
    }

    QualityPreset GetPresetFromName(const char* name)
    {
        if (!name) return PRESET_BALANCED;
        
        if (strcmp(name, "Ultra Fast") == 0) return PRESET_ULTRA_FAST;
        if (strcmp(name, "Fast") == 0) return PRESET_FAST;
        if (strcmp(name, "Balanced") == 0) return PRESET_BALANCED;
        if (strcmp(name, "Quality") == 0) return PRESET_QUALITY;
        if (strcmp(name, "Ultra Quality") == 0) return PRESET_ULTRA_QUALITY;
        
        return PRESET_BALANCED; // Default
    }

    void OptimizeForCurrentSystem(PerformanceProfile* profile)
    {
        if (!profile) return;

        // Detect system capabilities and optimize accordingly
        SYSTEM_INFO sysInfo;
        GetSystemInfo(&sysInfo);
        
        MEMORYSTATUSEX memStatus;
        memStatus.dwLength = sizeof(memStatus);
        GlobalMemoryStatusEx(&memStatus);
        
        // Adjust settings based on system specs
        if (sysInfo.dwNumberOfProcessors >= 8 && memStatus.ullTotalPhys >= (8ULL * 1024 * 1024 * 1024)) {
            // High-end system - can handle higher quality
            profile->quality.jpegQuality = min(profile->quality.jpegQuality + 10, 95);
            profile->quality.frameRateLimit = min(profile->quality.frameRateLimit + 15, 60);
        } else if (sysInfo.dwNumberOfProcessors <= 2 || memStatus.ullTotalPhys < (4ULL * 1024 * 1024 * 1024)) {
            // Low-end system - reduce quality for better performance
            profile->quality.jpegQuality = max(profile->quality.jpegQuality - 15, 25);
            profile->quality.frameRateLimit = max(profile->quality.frameRateLimit - 15, 15);
            profile->quality.adaptiveQuality = TRUE;
        }

        // Windows version specific optimizations
        OSVERSIONINFOEX osvi = { 0 };
        osvi.dwOSVersionInfoSize = sizeof(osvi);
        
        typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
        HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
        if (hMod) {
            RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
            if (RtlGetVersion) {
                RtlGetVersion((PRTL_OSVERSIONINFOW)&osvi);
                if (osvi.dwMajorVersion >= 10 && osvi.dwBuildNumber >= 22000) {
                    // Windows 11
                    profile->windows.optimizeForWindows11 = TRUE;
                    profile->windows.useDirectCapture = TRUE;
                }
            }
        }
    }

} // namespace PerformanceConfig
