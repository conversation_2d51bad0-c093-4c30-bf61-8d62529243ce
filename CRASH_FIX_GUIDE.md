# 🛠️ HVNC Crash Fix Guide

## 🚨 If the Application Crashes After Connection

The optimized HVNC includes advanced performance features that might cause crashes on some systems. Here are the solutions:

## 🔧 Quick Fix: Enable Safe Mode

**If you're experiencing crashes, run this immediately:**

```batch
enable_safe_mode.bat
```

This will:
- ✅ Disable all advanced optimizations
- ✅ Use conservative settings
- ✅ Prevent Windows 11 specific features from causing issues
- ✅ Disable hardware acceleration
- ✅ Use basic JPEG quality (50%)

## 📋 Manual Safe Mode Configuration

If the batch file doesn't work, manually edit `hvnc_performance.ini`:

```ini
# Safe Mode Settings
jpeg_quality=50
frame_rate=30
compression_level=5
hardware_accel=false
adaptive_quality=false
direct_capture=false
windows11_optimizations=false
use_compression=false
```

## 🔍 Common Crash Causes & Solutions

### 1. **DWM API Crashes (Windows 11)**
**Symptoms**: Crash immediately when window opens
**Solution**: 
```ini
windows11_optimizations=false
direct_capture=false
```

### 2. **Timer Resolution Issues**
**Symptoms**: Crash during initialization
**Solution**: The new version automatically handles this with fallbacks

### 3. **Hardware Acceleration Problems**
**Symptoms**: Visual artifacts or crashes during screen capture
**Solution**:
```ini
hardware_accel=false
```

### 4. **Memory Issues**
**Symptoms**: Crash after running for a while
**Solution**:
```ini
adaptive_quality=true
frame_rate=15
jpeg_quality=30
```

## 🎯 Step-by-Step Troubleshooting

### Step 1: Try Safe Mode
1. Run `enable_safe_mode.bat`
2. Test the application
3. If it works, gradually enable features

### Step 2: Gradual Feature Testing
If safe mode works, try enabling features one by one:

1. **Enable basic optimizations**:
   ```ini
   jpeg_quality=60
   frame_rate=45
   ```

2. **Test hardware acceleration**:
   ```ini
   hardware_accel=true
   ```

3. **Test Windows 11 features** (only on Windows 11):
   ```ini
   windows11_optimizations=true
   ```

4. **Enable adaptive quality**:
   ```ini
   adaptive_quality=true
   ```

### Step 3: System-Specific Adjustments

**For Older Systems (Windows 7/8)**:
```ini
windows11_optimizations=false
direct_capture=false
hardware_accel=false
frame_rate=30
```

**For Windows 10**:
```ini
windows11_optimizations=false
direct_capture=true
hardware_accel=true
frame_rate=45
```

**For Windows 11**:
```ini
windows11_optimizations=true
direct_capture=true
hardware_accel=true
frame_rate=60
```

## 🖥️ System Compatibility

### ✅ Tested Compatible Systems
- Windows 10 (Build 1903+)
- Windows 11 (All builds)
- Systems with 4GB+ RAM
- Modern graphics drivers

### ⚠️ Potentially Problematic Systems
- Windows 7/8 (use safe mode)
- Very old graphics drivers
- Systems with less than 2GB RAM
- Virtual machines (disable hardware acceleration)

## 🔧 Advanced Troubleshooting

### Check Windows Version
The application automatically detects your Windows version, but you can verify:
```batch
winver
```

### Update Graphics Drivers
Outdated drivers can cause crashes:
1. Visit your GPU manufacturer's website
2. Download latest drivers
3. Restart after installation

### Disable Windows Visual Effects
For maximum compatibility:
1. Right-click "This PC" → Properties
2. Advanced system settings
3. Performance → Settings
4. Choose "Adjust for best performance"

## 📊 Performance vs Stability Matrix

| Setting | Performance | Stability | Recommended For |
|---------|-------------|-----------|-----------------|
| Safe Mode | Low | Highest | Troubleshooting |
| Conservative | Medium | High | Older systems |
| Balanced | High | Medium | Most users |
| Maximum | Highest | Lower | Gaming/Modern systems |

## 🚀 Optimized Settings by Use Case

### Gaming (Maximum Performance)
```ini
jpeg_quality=40
frame_rate=45
hardware_accel=true
adaptive_quality=true
windows11_optimizations=true
```

### Office Work (Balanced)
```ini
jpeg_quality=60
frame_rate=60
hardware_accel=true
adaptive_quality=true
windows11_optimizations=true
```

### Older Systems (Maximum Stability)
```ini
jpeg_quality=50
frame_rate=30
hardware_accel=false
adaptive_quality=false
windows11_optimizations=false
```

## 📞 Still Having Issues?

If crashes persist after trying safe mode:

1. **Check the configuration file**: Ensure `hvnc_performance.ini` exists and has valid settings
2. **Try different presets**: Use `configure_performance.bat` to try different presets
3. **Check system requirements**: Ensure your system meets minimum requirements
4. **Disable antivirus temporarily**: Some antivirus software may interfere

## 🔄 Reset to Defaults

To completely reset all settings:
```batch
del hvnc_performance.ini
configure_performance.bat
```
Then select option 2 (Office preset) for balanced settings.

## 📝 Regarding Qt6 vs Current GUI

**Current GUI (Win32)**: 
- ✅ Faster startup
- ✅ Lower memory usage
- ✅ Better Windows integration
- ✅ No external dependencies

**Qt6 GUI**:
- ✅ More modern appearance
- ✅ Cross-platform potential
- ❌ Larger file size (~50MB+ vs 300KB)
- ❌ Slower startup
- ❌ More memory usage
- ❌ Additional dependencies

**Recommendation**: The current Win32 GUI is significantly faster and more efficient for this application. Qt6 would add unnecessary overhead for a Windows-specific tool.
