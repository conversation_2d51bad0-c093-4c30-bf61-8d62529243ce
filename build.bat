@echo off
REM Build script for HVNC.sln using MSBuild

REM Try to find MSBuild in common locations
set "MSBUILD_PATH="

REM VS2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2022 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

REM VS2019 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

if "%MSBUILD_PATH%"=="" (
    echo MSBuild.exe not found. Please install Visual Studio or Build Tools and update the path in this script.
    exit /b 1
)

echo Building HVNC solution...
"%MSBUILD_PATH%" HVNC.sln /p:Configuration=Release /p:OutDir=build\
if errorlevel 1 (
    echo Build failed.
    exit /b 1
)

echo Building Performance Optimizer tool...
if not exist "tools\build" mkdir "tools\build"
cl /EHsc /I. tools\PerformanceOptimizer.cpp common\PerformanceConfig.cpp /Fe:tools\build\PerformanceOptimizer.exe
if errorlevel 1 (
    echo Performance Optimizer build failed, but main build succeeded.
) else (
    echo Performance Optimizer built successfully.
)

echo.
echo Build completed successfully!
echo.
echo Main executables are in: build\
echo Performance Optimizer is in: tools\build\
echo Configuration file: hvnc_performance.ini
echo.
echo To optimize performance:
echo 1. Run tools\build\PerformanceOptimizer.exe
echo 2. Or manually edit hvnc_performance.ini
echo 3. See PERFORMANCE_GUIDE.md for detailed instructions
